#!/usr/bin/env python3
"""
Augment Code API - Put User on Plan Script
用于请求 Augment Code API 的用户计划变更脚本

使用方法:
python put_user_on_plan.py <session_value>

示例:
python put_user_on_plan.py "eyJvYXV0aDI6c3RhdGUiOiJjbGl1SUh3OWpLb0swVGh2TjRZdXZMSWg5MWdwbU5pZ2t6aXhuck00YjVNIi..."
"""

import sys
import requests
import json


def put_user_on_plan(session_value, plan_id="orb_community_plan"):
    """
    请求 Augment Code API 变更用户计划
    
    Args:
        session_value (str): _session cookie 值
        plan_id (str): 计划ID，默认为 "orb_community_plan"
    
    Returns:
        dict: API 响应结果
    """
    
    url = "https://app.augmentcode.com/api/put-user-on-plan"
    
    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://app.augmentcode.com',
        'priority': 'u=1, i',
        'referer': 'https://app.augmentcode.com/account/subscription',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    # 构建 cookie
    cookies = {
        '_session': session_value,
        'ajs_user_id': '4159609f-cf47-479f-9bcc-0c808882f30f',
        'ajs_anonymous_id': '5e03dcc8-c9a9-45be-a555-0936b2dac9a6'
    }
    
    # 请求数据
    data = {
        "planId": plan_id
    }
    
    try:
        print(f"正在请求 API: {url}")
        print(f"计划ID: {plan_id}")
        print("-" * 50)
        
        response = requests.post(
            url,
            headers=headers,
            cookies=cookies,
            json=data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("请求成功!")
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON 解析错误: {e}")
        print(f"原始响应: {response.text}")
        return None


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("错误: 缺少必需的参数")
        print("使用方法: python put_user_on_plan.py <session_value> [plan_id]")
        print("示例: python put_user_on_plan.py \"eyJvYXV0aDI6c3RhdGUi...\"")
        sys.exit(1)
    
    session_value = sys.argv[1]
    plan_id = sys.argv[2] if len(sys.argv) > 2 else "orb_community_plan"
    
    if not session_value:
        print("错误: session_value 不能为空")
        sys.exit(1)
    
    print("Augment Code API - 用户计划变更脚本")
    print("=" * 50)
    
    result = put_user_on_plan(session_value, plan_id)
    
    if result and result.get('success'):
        print("\n✅ 计划变更成功!")
        if 'message' in result:
            print(f"消息: {result['message']}")
    else:
        print("\n❌ 计划变更失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
