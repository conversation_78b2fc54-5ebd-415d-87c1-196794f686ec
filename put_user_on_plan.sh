#!/bin/bash

# Augment Code API - Put User on Plan Script
# 用于请求 Augment Code API 的用户计划变更脚本
#
# 使用方法:
# ./put_user_on_plan.sh <session_value> [plan_id]
#
# 示例:
# ./put_user_on_plan.sh "eyJvYXV0aDI6c3RhdGUiOiJjbGl1SUh3OWpLb0swVGh2TjRZdXZMSWg5MWdwbU5pZ2t6aXhuck00YjVNIi..."

# 检查参数
if [ $# -lt 1 ]; then
    echo "错误: 缺少必需的参数"
    echo "使用方法: $0 <session_value> [plan_id]"
    echo "示例: $0 \"eyJvYXV0aDI6c3RhdGUi...\""
    exit 1
fi

SESSION_VALUE="$1"
PLAN_ID="${2:-orb_community_plan}"

if [ -z "$SESSION_VALUE" ]; then
    echo "错误: session_value 不能为空"
    exit 1
fi

echo "Augment Code API - 用户计划变更脚本"
echo "=================================================="
echo "正在请求 API: https://app.augmentcode.com/api/put-user-on-plan"
echo "计划ID: $PLAN_ID"
echo "--------------------------------------------------"

# 执行 curl 请求
response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
    'https://app.augmentcode.com/api/put-user-on-plan' \
    -H 'accept: */*' \
    -H 'accept-language: zh-CN,zh;q=0.9' \
    -H 'content-type: application/json' \
    -b "_session=$SESSION_VALUE; ajs_user_id=4159609f-cf47-479f-9bcc-0c808882f30f; ajs_anonymous_id=5e03dcc8-c9a9-45be-a555-0936b2dac9a6" \
    -H 'origin: https://app.augmentcode.com' \
    -H 'priority: u=1, i' \
    -H 'referer: https://app.augmentcode.com/account/subscription' \
    -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "Windows"' \
    -H 'sec-fetch-dest: empty' \
    -H 'sec-fetch-mode: cors' \
    -H 'sec-fetch-site: same-origin' \
    -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    --data-raw "{\"planId\":\"$PLAN_ID\"}")

# 分离响应体和状态码
http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')

echo "响应状态码: $http_status"

if [ "$http_status" = "200" ]; then
    echo "请求成功!"
    echo "响应内容:"
    echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"
    
    # 检查是否成功
    if echo "$response_body" | grep -q '"success":true'; then
        echo ""
        echo "✅ 计划变更成功!"
    else
        echo ""
        echo "❌ 计划变更失败!"
        exit 1
    fi
else
    echo "请求失败: HTTP $http_status"
    echo "响应内容: $response_body"
    exit 1
fi
