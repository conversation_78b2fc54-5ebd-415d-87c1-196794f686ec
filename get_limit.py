import requests
import json

# cookie固定的前缀
prefix = 'user_01000000000000000000000000%3A%3A'
# 填写账号token部分
token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlFEOTEzWkEzTURUWUdWWlFKV0U0SkZUIiwidGltZSI6IjE3NDMxMjg2NzciLCJyYW5kb21uZXNzIjoiN2Y0ZjgxNGYtMWFkZS00NmEzIiwiZXhwIjo0MzM1MTI4Njc3LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.wukoOGcikLhLYU-rlNhpaD9dZpV1hD8OBC8sw69BNuw'

# 拼接完整的token
full_token = prefix + token

cookies = {
    'WorkosCursorSessionToken': full_token
}

headers = {
    'authority': 'www.cursor.com',
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'dnt': '1',
    'referer': 'https://www.cursor.com/cn/settings',
    'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
}

response = requests.get('https://www.cursor.com/api/usage', cookies=cookies, headers=headers)

# 显示原始响应
print("API响应：", response.text)

# 添加判断逻辑
try:
    response_json = response.json()
    
    # 检查是否包含错误信息
    if "error" in response_json or "statusCode" in response_json:
        error_message = response_json.get("message", "未知错误")
        status_code = response_json.get("statusCode", "")
        error_type = response_json.get("error", "")
        error_code = response_json.get("code", "")
        
        print(f"\n❌ 获取额度失败！")
        print(f"  - 状态码: {status_code}")
        print(f"  - 错误类型: {error_type}")
        print(f"  - 错误代码: {error_code}")
        print(f"  - 错误消息: {error_message}")
    
    # 检查是否包含各种模型的用量信息，表示成功
    elif "gpt-4" in response_json and "startOfMonth" in response_json:
        print("\n✅ 获取额度成功！")
        
        # 格式化计费周期开始日期
        start_date = response_json.get("startOfMonth", "").split("T")[0]
        print(f"\n📅 计费周期开始日期: {start_date}")
        
        # 遍历显示各个模型的额度信息
        models = ["gpt-4", "gpt-3.5-turbo", "gpt-4-32k"]
        for model in models:
            if model in response_json:
                model_data = response_json[model]
                print(f"\n🤖 {model} 模型:")
                print(f"  - 已使用请求数: {model_data.get('numRequests', 0)}")
                print(f"  - 总请求数: {model_data.get('numRequestsTotal', 0)}")
                print(f"  - 已使用token数: {model_data.get('numTokens', 0)}")
                
                max_requests = model_data.get('maxRequestUsage')
                if max_requests is not None:
                    print(f"  - 最大请求限制: {max_requests}")
                else:
                    print(f"  - 最大请求限制: 无限制")
                
                max_tokens = model_data.get('maxTokenUsage')
                if max_tokens is not None:
                    print(f"  - 最大token限制: {max_tokens}")
                else:
                    print(f"  - 最大token限制: 无限制")
    else:
        print("\n❓ 未知响应格式")
        
except json.JSONDecodeError:
    print("\n❌ 获取额度失败：响应格式无效")
except Exception as e:
    print(f"\n❌ 处理响应时出错：{str(e)}")
