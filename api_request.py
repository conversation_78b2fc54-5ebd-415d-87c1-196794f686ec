#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Code API请求脚本
用于发送put-user-on-plan API请求
"""

import requests
import json
import sys


def send_api_request(session_value, plan_id="orb_community_plan"):
    """
    发送API请求到Augment Code
    
    Args:
        session_value (str): _session cookie的值
        plan_id (str): 计划ID，默认为"orb_community_plan"
    
    Returns:
        dict: API响应结果
    """
    
    # API端点
    url = "https://app.augmentcode.com/api/put-user-on-plan"
    
    # 请求头
    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://app.augmentcode.com',
        'priority': 'u=1, i',
        'referer': 'https://app.augmentcode.com/account/subscription',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    # Cookies
    cookies = {
        '_session': session_value,
        'ajs_user_id': '4159609f-cf47-479f-9bcc-0c808882f30f',
        'ajs_anonymous_id': '5e03dcc8-c9a9-45be-a555-0936b2dac9a6'
    }
    
    # 请求数据
    data = {
        "planId": plan_id
    }
    
    try:
        # 发送POST请求
        response = requests.post(
            url=url,
            headers=headers,
            cookies=cookies,
            json=data,
            timeout=30
        )
        
        # 检查响应状态
        response.raise_for_status()
        
        # 返回JSON响应
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return None


def main():
    """主函数"""
    print("=== Augment Code API请求工具 ===\n")
    
    # 获取session值
    if len(sys.argv) > 1:
        session_value = sys.argv[1]
    else:
        session_value = input("请输入_session值: ").strip()
    
    if not session_value:
        print("错误: _session值不能为空")
        sys.exit(1)
    
    # 获取计划ID（可选）
    plan_id = input("请输入planId (默认: orb_community_plan): ").strip()
    if not plan_id:
        plan_id = "orb_community_plan"
    
    print(f"\n正在发送请求...")
    print(f"计划ID: {plan_id}")
    
    # 发送请求
    result = send_api_request(session_value, plan_id)
    
    if result:
        print("\n=== 响应结果 ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 检查是否成功
        if result.get('success'):
            print(f"\n✅ 成功: {result.get('message', '操作完成')}")
        else:
            print(f"\n❌ 失败: {result.get('message', '未知错误')}")
    else:
        print("\n❌ 请求失败，请检查网络连接和session值")


if __name__ == "__main__":
    main()
