#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Code API请求脚本
用于发送put-user-on-plan API请求
"""

import requests
import json
import sys
from datetime import datetime

# 配置区域 - 在这里设置你的session值
SESSION_VALUE = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.L55G%2FRvFCQIdtrAcyGapvSP0nzbc2ediJIvPPB8b%2BwY"

# 美化输出的颜色代码
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'


def print_header():
    """打印美化的标题"""
    print(f"{Colors.CYAN}{Colors.BOLD}{'='*60}{Colors.END}")
    print(f"{Colors.CYAN}{Colors.BOLD}    🚀 Augment Code 订阅计划切换工具 🚀{Colors.END}")
    print(f"{Colors.CYAN}{Colors.BOLD}{'='*60}{Colors.END}")
    print()

def print_status(message, status="info"):
    """打印状态信息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    if status == "success":
        print(f"{Colors.GREEN}✅ [{timestamp}] {message}{Colors.END}")
    elif status == "error":
        print(f"{Colors.RED}❌ [{timestamp}] {message}{Colors.END}")
    elif status == "warning":
        print(f"{Colors.YELLOW}⚠️  [{timestamp}] {message}{Colors.END}")
    else:
        print(f"{Colors.BLUE}ℹ️  [{timestamp}] {message}{Colors.END}")

def print_response(response_data):
    """美化打印API响应"""
    print(f"\n{Colors.PURPLE}{Colors.BOLD}📋 API响应详情:{Colors.END}")
    print(f"{Colors.WHITE}{'─'*50}{Colors.END}")

    # 解析响应数据
    if isinstance(response_data, dict):
        for key, value in response_data.items():
            if key == "success":
                status_icon = "✅" if value else "❌"
                color = Colors.GREEN if value else Colors.RED
                print(f"{Colors.CYAN}📊 状态: {color}{status_icon} {value}{Colors.END}")
            elif key == "type":
                print(f"{Colors.CYAN}🏷️  类型: {Colors.WHITE}{value}{Colors.END}")
            elif key == "message":
                print(f"{Colors.CYAN}💬 消息: {Colors.WHITE}{value}{Colors.END}")
            else:
                print(f"{Colors.CYAN}{key}: {Colors.WHITE}{value}{Colors.END}")

    print(f"{Colors.WHITE}{'─'*50}{Colors.END}")

def send_api_request(session_value, plan_id="orb_community_plan"):
    """
    发送API请求到Augment Code

    Args:
        session_value (str): _session cookie的值
        plan_id (str): 计划ID，默认为"orb_community_plan"

    Returns:
        dict: API响应结果
    """
    
    # API端点
    url = "https://app.augmentcode.com/api/put-user-on-plan"
    
    # 请求头
    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'origin': 'https://app.augmentcode.com',
        'priority': 'u=1, i',
        'referer': 'https://app.augmentcode.com/account/subscription',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    # Cookies
    cookies = {
        '_session': session_value,
        'ajs_user_id': '4159609f-cf47-479f-9bcc-0c808882f30f',
        'ajs_anonymous_id': '5e03dcc8-c9a9-45be-a555-0936b2dac9a6'
    }
    
    # 请求数据
    data = {
        "planId": plan_id
    }
    
    try:
        print_status("正在发送API请求...", "info")

        # 发送POST请求
        response = requests.post(
            url=url,
            headers=headers,
            cookies=cookies,
            json=data,
            timeout=30
        )

        # 检查响应状态
        response.raise_for_status()
        print_status(f"请求成功 (状态码: {response.status_code})", "success")

        # 返回JSON响应
        return response.json()

    except requests.exceptions.RequestException as e:
        print_status(f"网络请求失败: {e}", "error")
        return None
    except json.JSONDecodeError as e:
        print_status(f"响应JSON解析失败: {e}", "error")
        return None


def main():
    """主函数"""
    print_header()

    # 检查session值是否已配置
    if not SESSION_VALUE or SESSION_VALUE == "your_session_value_here":
        print_status("请先在脚本顶部配置SESSION_VALUE", "error")
        print(f"{Colors.YELLOW}💡 提示: 编辑脚本文件，将SESSION_VALUE设置为你的实际session值{Colors.END}")
        sys.exit(1)

    print_status("使用预配置的session值", "info")

    # 获取计划ID（可选）
    print(f"{Colors.CYAN}📋 可用的计划类型:{Colors.END}")
    print(f"   • {Colors.WHITE}orb_community_plan{Colors.END} (社区计划)")
    print(f"   • {Colors.WHITE}orb_pro_plan{Colors.END} (专业计划)")
    print(f"   • {Colors.WHITE}orb_enterprise_plan{Colors.END} (企业计划)")
    print()

    plan_id = input(f"{Colors.CYAN}🎯 请输入planId (默认: orb_community_plan): {Colors.END}").strip()
    if not plan_id:
        plan_id = "orb_community_plan"

    print()
    print_status(f"目标计划: {plan_id}", "info")

    # 发送请求
    result = send_api_request(SESSION_VALUE, plan_id)

    if result:
        print_response(result)

        # 检查是否成功
        if result.get('success'):
            print_status("订阅计划切换成功! 🎉", "success")
        else:
            print_status(f"操作失败: {result.get('message', '未知错误')}", "error")
    else:
        print_status("请求失败，请检查网络连接和session值", "error")

    print(f"\n{Colors.CYAN}{'='*60}{Colors.END}")


if __name__ == "__main__":
    main()
