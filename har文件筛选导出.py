#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HAR文件筛选工具

此脚本用于筛选.har文件中的网络请求，并将结果输出到新的.har文件。
支持多种筛选条件，如资源类型、状态码、大小、时间和URL关键词。
"""

import json
import os
import datetime
import sys
import argparse
import logging
import traceback
from typing import Dict, List, Any, Callable, Optional, Union
from colorama import init, Fore, Back, Style

# 初始化colorama
init(autoreset=True)

# 终端美化函数
def print_header(text: str) -> None:
    """打印带颜色的标题"""
    print(f"\n{Fore.CYAN}{Style.BRIGHT}===== {text} ====={Style.RESET_ALL}")

def print_subheader(text: str) -> None:
    """打印带颜色的子标题"""
    print(f"\n{Fore.BLUE}{Style.BRIGHT}--- {text} ---{Style.RESET_ALL}")

def print_success(text: str) -> None:
    """打印成功信息"""
    print(f"{Fore.GREEN}{Style.BRIGHT}✅ {text}{Style.RESET_ALL}")

def print_info(text: str) -> None:
    """打印一般信息"""
    print(f"{Fore.WHITE}ℹ️ {text}{Style.RESET_ALL}")

def print_warning(text: str) -> None:
    """打印警告信息"""
    print(f"{Fore.YELLOW}⚠️ {text}{Style.RESET_ALL}")

def print_error(text: str) -> None:
    """打印错误信息"""
    print(f"{Fore.RED}❌ {text}{Style.RESET_ALL}")

def print_processing(text: str) -> None:
    """打印处理中信息"""
    print(f"{Fore.MAGENTA}🔄 {text}{Style.RESET_ALL}")

def print_filter_summary(filter_type: str, value: str) -> None:
    """打印筛选条件摘要"""
    print(f"{Fore.CYAN}🔍 {filter_type}: {Fore.WHITE}{value}{Style.RESET_ALL}")

def print_stats(text: str) -> None:
    """打印统计信息"""
    print(f"{Fore.YELLOW}📊 {text}{Style.RESET_ALL}")

def print_file_info(text: str) -> None:
    """打印文件信息"""
    print(f"{Fore.GREEN}📁 {text}{Style.RESET_ALL}")

# 设置日志记录
def setup_logging(verbose: bool = False) -> None:
    """
    设置日志记录
    
    Args:
        verbose: 是否启用详细日志
    """
    log_level = logging.DEBUG if verbose else logging.INFO
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(),
        ]
    )

def read_har_file(file_path: str) -> Dict[str, Any]:
    """
    读取.har文件并返回其JSON内容
    
    Args:
        file_path: .har文件的路径
        
    Returns:
        Dict: 包含.har文件内容的字典
        
    Raises:
        Exception: 如果文件读取或解析失败
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            har_data = json.load(f)
        
        # 验证基本结构
        if 'log' not in har_data or 'entries' not in har_data['log']:
            raise ValueError("无效的HAR文件格式：缺少log或entries字段")
            
        return har_data
    except json.JSONDecodeError:
        logging.error(f"无法解析HAR文件：'{file_path}'，格式不是有效的JSON")
        raise ValueError(f"无法解析HAR文件：'{file_path}'，格式不是有效的JSON")
    except Exception as e:
        logging.error(f"读取HAR文件时出错：{str(e)}")
        raise Exception(f"读取HAR文件时出错：{str(e)}")

def get_entries(har_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    从HAR数据中提取所有请求条目
    
    Args:
        har_data: HAR文件的JSON内容
        
    Returns:
        List: HAR条目列表
    """
    return har_data['log']['entries']

# 资源类型筛选函数
def filter_by_resource_type(entries: List[Dict[str, Any]], resource_type: Union[str, List[str]]) -> List[Dict[str, Any]]:
    """
    根据资源类型筛选条目
    
    Args:
        entries: HAR条目列表
        resource_type: 要筛选的资源类型，可以是单个字符串或字符串列表
        
    Returns:
        List: 筛选后的HAR条目列表
    """
    # 如果是列表且包含"all"，则返回所有条目
    if isinstance(resource_type, list) and "all" in resource_type:
        return entries
    
    # 如果是字符串且为"all"，则返回所有条目
    if isinstance(resource_type, str) and resource_type.lower() == "all":
        return entries
    
    filtered_entries = []
    
    # 资源类型映射（与Chrome DevTools一致）
    resource_type_mapping = {
        "all": None,  # 全部
        "xhr": ["xhr"],  # XHR请求
        "fetch": ["fetch"],  # Fetch请求
        "js": ["script", "javascript"],  # JavaScript
        "css": ["stylesheet", "css"],  # CSS
        "img": ["image", "png", "jpeg", "jpg", "gif", "webp", "svg", "ico"],  # 图片
        "media": ["media", "audio", "video"],  # 媒体
        "font": ["font"],  # 字体
        "doc": ["document", "text/html"],  # 文档
        "ws": ["websocket"],  # WebSocket
        "wasm": ["wasm"],  # WebAssembly
        "manifest": ["manifest"],  # Manifest
        "other": ["other", "eventsource", "ping"]  # 其他
    }
    
    # 将资源类型转换为列表
    resource_types = [resource_type] if isinstance(resource_type, str) else resource_type
    
    # 处理所有的资源类型
    all_target_types = []
    for rt in resource_types:
        # Chrome DevTools中Fetch/XHR合并显示，这里特殊处理
        if rt.lower() == "fetch/xhr":
            all_target_types.extend(resource_type_mapping.get("xhr", []) + resource_type_mapping.get("fetch", []))
        else:
            # 获取目标资源类型的映射值
            all_target_types.extend(resource_type_mapping.get(rt.lower(), [rt.lower()]))
    
    # 去重
    all_target_types = list(set(all_target_types))
    
    for entry in entries:
        # 优先使用_resourceType字段（Chrome DevTools特有）
        entry_resource_type = entry.get("_resourceType", "").lower()
        
        # 从response的content-type或mimeType获取资源类型
        mime_type = ""
        if "response" in entry and "content" in entry["response"]:
            mime_type = entry["response"]["content"].get("mimeType", "").lower()
        
        # 获取URL和文件扩展名
        url = entry.get("request", {}).get("url", "").lower()
        file_extension = os.path.splitext(url.split("?")[0].split("#")[0])[-1].lower()
        
        # 初始化资源类型标识符
        is_target_type = False
        
        # 对每个资源类型进行检查
        for resource_type in resource_types:
            # 1. 优先检查_resourceType字段
            if entry_resource_type:
                if resource_type.lower() == "fetch/xhr" and entry_resource_type in ["xhr", "fetch"]:
                    is_target_type = True
                    break
                elif any(t == entry_resource_type for t in all_target_types):
                    is_target_type = True
                    break
            
            # 2. 如果_resourceType不存在或不匹配，检查MIME类型和URL特征
            # 检查MIME类型
            if mime_type:
                if resource_type.lower() == "doc" and ("text/html" in mime_type or "application/xhtml" in mime_type):
                    is_target_type = True
                    break
                elif resource_type.lower() == "js" and ("javascript" in mime_type or "ecmascript" in mime_type):
                    is_target_type = True
                    break
                elif resource_type.lower() == "css" and "text/css" in mime_type:
                    is_target_type = True
                    break
                elif resource_type.lower() == "img" and "image/" in mime_type:
                    is_target_type = True
                    break
                elif resource_type.lower() == "font" and ("font" in mime_type or "woff" in mime_type or "ttf" in mime_type or "otf" in mime_type):
                    is_target_type = True
                    break
                elif resource_type.lower() == "media" and ("audio/" in mime_type or "video/" in mime_type):
                    is_target_type = True
                    break
                elif resource_type.lower() == "fetch/xhr" and ("json" in mime_type or "xml" in mime_type or "text/plain" in mime_type):
                    is_target_type = True
                    break
            
            # 检查文件扩展名
            if not is_target_type and file_extension:
                if resource_type.lower() == "js" and file_extension in [".js", ".mjs", ".jsx"]:
                    is_target_type = True
                    break
                elif resource_type.lower() == "css" and file_extension == ".css":
                    is_target_type = True
                    break
                elif resource_type.lower() == "img" and file_extension in [".png", ".jpg", ".jpeg", ".gif", ".webp", ".svg", ".ico"]:
                    is_target_type = True
                    break
                elif resource_type.lower() == "font" and file_extension in [".woff", ".woff2", ".ttf", ".eot", ".otf"]:
                    is_target_type = True
                    break
                elif resource_type.lower() == "media" and file_extension in [".mp3", ".mp4", ".wav", ".ogg", ".webm"]:
                    is_target_type = True
                    break
                elif resource_type.lower() == "doc" and file_extension in [".html", ".htm"]:
                    is_target_type = True
                    break
        
        # 如果匹配目标类型，添加到筛选结果
        if is_target_type:
            filtered_entries.append(entry)
    
    return filtered_entries

# 状态码筛选函数
def filter_by_status(entries: List[Dict[str, Any]], status_filter: str) -> List[Dict[str, Any]]:
    """
    根据HTTP状态码筛选条目
    
    Args:
        entries: HAR条目列表
        status_filter: 状态码筛选条件，如"200"、"2xx"、"成功"等
        
    Returns:
        List: 筛选后的HAR条目列表
    """
    filtered_entries = []
    
    # 状态码类型映射
    status_mapping = {
        "成功": range(200, 300),      # 2xx
        "重定向": range(300, 400),    # 3xx
        "客户端错误": range(400, 500), # 4xx
        "服务器错误": range(500, 600), # 5xx
        "错误": list(range(400, 500)) + list(range(500, 600))  # 4xx和5xx
    }
    
    # 如果筛选条件是"全部"，则返回所有条目
    if status_filter.lower() in ["all", "全部"]:
        return entries
    
    for entry in entries:
        if "response" in entry and "status" in entry["response"]:
            status = entry["response"]["status"]
            
            # 如果筛选条件是具体的状态码
            if status_filter.isdigit() and int(status_filter) == status:
                filtered_entries.append(entry)
                continue
                
            # 如果筛选条件是状态码范围，如"2xx"
            if len(status_filter) == 3 and status_filter[0].isdigit() and status_filter[1:] == "xx":
                status_range = range(int(status_filter[0]) * 100, (int(status_filter[0]) + 1) * 100)
                if status in status_range:
                    filtered_entries.append(entry)
                    continue
            
            # 如果筛选条件是中文描述，如"成功"
            for desc, range_values in status_mapping.items():
                if status_filter == desc and status in range_values:
                    filtered_entries.append(entry)
                    break
    
    return filtered_entries

# 大小筛选函数
def filter_by_size(entries: List[Dict[str, Any]], size_filter: str) -> List[Dict[str, Any]]:
    """
    根据请求/响应大小筛选条目
    
    Args:
        entries: HAR条目列表
        size_filter: 大小筛选条件，如">100KB"、"<1MB"等
        
    Returns:
        List: 筛选后的HAR条目列表
    """
    filtered_entries = []
    
    # 如果筛选条件是"全部"，则返回所有条目
    if size_filter.lower() in ["all", "全部"]:
        return entries
    
    # 解析筛选条件
    size_filter = size_filter.strip()
    if not size_filter:
        return entries
    
    # 解析操作符和大小值
    operator = ""
    if size_filter.startswith(">"):
        operator = ">"
        size_filter = size_filter[1:].strip()
    elif size_filter.startswith("<"):
        operator = "<"
        size_filter = size_filter[1:].strip()
    elif size_filter.startswith("="):
        operator = "="
        size_filter = size_filter[1:].strip()
    else:
        # 如果没有指定操作符，默认为等于
        operator = "="
    
    # 解析大小值和单位
    size_value = ""
    size_unit = ""
    for i, char in enumerate(size_filter):
        if not char.isdigit() and char != '.':
            size_value = size_filter[:i].strip()
            size_unit = size_filter[i:].strip().upper()
            break
    else:
        size_value = size_filter
        size_unit = "B"  # 默认单位为字节
    
    # 转换大小值为浮点数
    try:
        size_value = float(size_value)
    except ValueError:
        print(f"警告: 无效的大小值 '{size_value}'，使用默认值0")
        size_value = 0
    
    # 转换单位为字节数
    unit_multiplier = {
        "B": 1,
        "KB": 1024,
        "MB": 1024 * 1024,
        "GB": 1024 * 1024 * 1024
    }
    
    # 获取单位乘数
    multiplier = unit_multiplier.get(size_unit, 1)
    target_size_bytes = size_value * multiplier
    
    for entry in entries:
        # 获取请求和响应的大小
        request_size = 0
        response_size = 0
        
        if "request" in entry:
            # 请求头部大小
            request_size += entry["request"].get("headersSize", 0) or 0
            # 请求体大小
            request_size += entry["request"].get("bodySize", 0) or 0
        
        if "response" in entry:
            # 响应头部大小
            response_size += entry["response"].get("headersSize", 0) or 0
            # 响应体大小
            response_size += entry["response"].get("bodySize", 0) or 0
            # 内容大小
            if "content" in entry["response"]:
                content_size = entry["response"]["content"].get("size", 0) or 0
                if content_size > 0 and (response_size == 0 or content_size > response_size):
                    response_size = content_size
        
        # 总大小
        total_size = request_size + response_size
        
        # 根据操作符筛选
        if operator == ">" and total_size > target_size_bytes:
            filtered_entries.append(entry)
        elif operator == "<" and total_size < target_size_bytes:
            filtered_entries.append(entry)
        elif operator == "=" and abs(total_size - target_size_bytes) / max(1, target_size_bytes) < 0.1:  # 允许10%的误差
            filtered_entries.append(entry)
    
    return filtered_entries

# 时间筛选函数
def filter_by_time(entries: List[Dict[str, Any]], time_filter: str) -> List[Dict[str, Any]]:
    """
    根据请求时间筛选条目
    
    Args:
        entries: HAR条目列表
        time_filter: 时间筛选条件，如">100ms"、"<1s"等
        
    Returns:
        List: 筛选后的HAR条目列表
    """
    filtered_entries = []
    
    # 如果筛选条件是"全部"，则返回所有条目
    if time_filter.lower() in ["all", "全部"]:
        return entries
    
    # 解析筛选条件
    time_filter = time_filter.strip()
    if not time_filter:
        return entries
    
    # 解析操作符和时间值
    operator = ""
    if time_filter.startswith(">"):
        operator = ">"
        time_filter = time_filter[1:].strip()
    elif time_filter.startswith("<"):
        operator = "<"
        time_filter = time_filter[1:].strip()
    elif time_filter.startswith("="):
        operator = "="
        time_filter = time_filter[1:].strip()
    else:
        # 如果没有指定操作符，默认为等于
        operator = "="
    
    # 解析时间值和单位
    time_value = ""
    time_unit = ""
    for i, char in enumerate(time_filter):
        if not char.isdigit() and char != '.':
            time_value = time_filter[:i].strip()
            time_unit = time_filter[i:].strip().lower()
            break
    else:
        time_value = time_filter
        time_unit = "ms"  # 默认单位为毫秒
    
    # 转换时间值为浮点数
    try:
        time_value = float(time_value)
    except ValueError:
        print(f"警告: 无效的时间值 '{time_value}'，使用默认值0")
        time_value = 0
    
    # 转换单位为毫秒
    unit_multiplier = {
        "ms": 1,
        "s": 1000,
        "sec": 1000,
        "m": 60 * 1000,
        "min": 60 * 1000
    }
    
    # 获取单位乘数
    multiplier = unit_multiplier.get(time_unit, 1)
    target_time_ms = time_value * multiplier
    
    for entry in entries:
        # 获取请求时间
        time_ms = 0
        
        # 使用time字段（毫秒）
        if "time" in entry:
            time_ms = entry["time"]
        # 如果没有time字段，计算从请求开始到响应结束的时间
        elif "startedDateTime" in entry and "timings" in entry:
            total_timing = 0
            for timing_key, timing_value in entry["timings"].items():
                if timing_key != "_blocked_queueing" and isinstance(timing_value, (int, float)) and timing_value > 0:
                    total_timing += timing_value
            time_ms = total_timing
        
        # 根据操作符筛选
        if operator == ">" and time_ms > target_time_ms:
            filtered_entries.append(entry)
        elif operator == "<" and time_ms < target_time_ms:
            filtered_entries.append(entry)
        elif operator == "=" and abs(time_ms - target_time_ms) / max(1, target_time_ms) < 0.1:  # 允许10%的误差
            filtered_entries.append(entry)
    
    return filtered_entries

# URL关键词筛选函数
def filter_by_url(entries: List[Dict[str, Any]], url_filter: str) -> List[Dict[str, Any]]:
    """
    根据URL关键词筛选条目
    
    Args:
        entries: HAR条目列表
        url_filter: URL关键词筛选条件，可以是部分URL或正则表达式
        
    Returns:
        List: 筛选后的HAR条目列表
    """
    filtered_entries = []
    
    # 如果筛选条件是"全部"或空，则返回所有条目
    if not url_filter or url_filter.lower() in ["all", "全部"]:
        return entries
    
    # 移除URL筛选条件首尾的引号
    url_filter = url_filter.strip('"\'')
    
    for entry in entries:
        if "request" in entry and "url" in entry["request"]:
            url = entry["request"]["url"]
            
            # 检查URL是否包含筛选关键词
            if url_filter.lower() in url.lower():
                filtered_entries.append(entry)
    
    return filtered_entries

# 交互式用户界面
def interactive_mode(input_file: str, output_file: Optional[str] = None, verbose: bool = False) -> None:
    """
    交互式模式，与用户交互并收集筛选选项
    
    Args:
        input_file: 输入.har文件路径
        output_file: 输出.har文件路径，如果为None则自动生成
        verbose: 是否显示详细信息
    """
    try:
        # 读取输入文件
        print_processing(f"正在读取文件: {input_file}...")
        har_data = read_har_file(input_file)
        entries = get_entries(har_data)
        print_success(f"成功读取 {len(entries)} 个请求条目。")
        
        # 收集筛选选项
        filters = {}
        
        # 显示菜单标题
        print_header("HAR文件筛选工具")
        
        # 1. 资源类型筛选
        print_subheader("资源类型筛选")
        resource_types = get_available_resource_types()
        for i, resource_type in enumerate(resource_types, 1):
            print(f"{Fore.CYAN}{i:2d}. {Fore.WHITE}{resource_type}{Style.RESET_ALL}")
        
        while True:
            try:
                choice = input(f"{Fore.YELLOW}请选择资源类型 (输入序号或名称，多选请用空格分隔，直接回车选择'全部'): {Style.RESET_ALL}").strip()
                if not choice:
                    resource_types_selected = ["全部"]
                    break
                
                # 处理多选
                choices = choice.split()
                resource_types_selected = []
                
                for single_choice in choices:
                    if single_choice.isdigit() and 1 <= int(single_choice) <= len(resource_types):
                        resource_types_selected.append(resource_types[int(single_choice) - 1])
                    elif single_choice in resource_types:
                        resource_types_selected.append(single_choice)
                    else:
                        print_warning(f"无效的选择: {single_choice}，已忽略")
                
                # 如果没有有效选择，提示重新输入
                if not resource_types_selected:
                    print_warning("没有有效的选择，请重新输入。")
                    continue
                
                break
            except Exception as e:
                print_error(f"输入错误: {e}")
        
        # 转换为英文代码
        resource_types_codes = [translate_resource_type(rt) for rt in resource_types_selected]
        filters["resource_type"] = resource_types_codes
        
        # 显示选择的资源类型
        resource_types_display = ", ".join(resource_types_selected)
        print_filter_summary("已选择资源类型", resource_types_display)
        
        # 2. 状态码筛选
        print_subheader("状态码筛选")
        status_filters = get_available_status_filters()
        for i, status_filter in enumerate(status_filters, 1):
            print(f"{Fore.CYAN}{i:2d}. {Fore.WHITE}{status_filter}{Style.RESET_ALL}")
        
        while True:
            try:
                choice = input(f"{Fore.YELLOW}请选择状态码筛选 (输入序号或名称，或输入具体状态码如'200'，直接回车选择'全部'): {Style.RESET_ALL}").strip()
                if not choice:
                    status_filter = "全部"
                    break
                
                if choice.isdigit() and 1 <= int(choice) <= len(status_filters):
                    status_filter = status_filters[int(choice) - 1]
                    break
                elif choice in status_filters:
                    status_filter = choice
                    break
                elif choice.isdigit() or (len(choice) == 3 and choice[0].isdigit() and choice[1:] == "xx"):
                    status_filter = choice
                    break
                else:
                    print_warning("无效的选择，请重新输入。")
            except Exception as e:
                print_error(f"输入错误: {e}")
        
        filters["status"] = translate_status_filter(status_filter)
        print_filter_summary("已选择状态码筛选", status_filter)
        
        # 3. 大小筛选
        print_subheader("大小筛选")
        while True:
            try:
                size_filter = input(f"{Fore.YELLOW}请输入大小筛选条件 (如'>100KB', '<1MB'，直接回车选择'全部'): {Style.RESET_ALL}").strip()
                if not size_filter:
                    size_filter = "全部"
                    break
                
                # 简单验证格式
                if size_filter.lower() == "全部" or size_filter.lower() == "all":
                    break
                
                valid_format = False
                
                # 检查是否以操作符开头
                if size_filter.startswith(">") or size_filter.startswith("<") or size_filter.startswith("="):
                    size_filter = size_filter[1:].strip()
                
                # 检查是否包含数字和单位
                for i, char in enumerate(size_filter):
                    if not char.isdigit() and char != '.':
                        size_value = size_filter[:i].strip()
                        size_unit = size_filter[i:].strip().upper()
                        if size_value and size_unit in ["B", "KB", "MB", "GB"]:
                            valid_format = True
                        break
                
                if valid_format or size_filter.isdigit():
                    break
                else:
                    print_warning("无效的格式，请输入类似'>100KB'的格式。")
            except Exception as e:
                print_error(f"输入错误: {e}")
        
        filters["size"] = size_filter
        print_filter_summary("已选择大小筛选", size_filter)
        
        # 4. 时间筛选
        print_subheader("时间筛选")
        while True:
            try:
                time_filter = input(f"{Fore.YELLOW}请输入时间筛选条件 (如'>100ms', '<1s'，直接回车选择'全部'): {Style.RESET_ALL}").strip()
                if not time_filter:
                    time_filter = "全部"
                    break
                
                # 简单验证格式
                if time_filter.lower() == "全部" or time_filter.lower() == "all":
                    break
                
                valid_format = False
                
                # 检查是否以操作符开头
                if time_filter.startswith(">") or time_filter.startswith("<") or time_filter.startswith("="):
                    time_filter = time_filter[1:].strip()
                
                # 检查是否包含数字和单位
                for i, char in enumerate(time_filter):
                    if not char.isdigit() and char != '.':
                        time_value = time_filter[:i].strip()
                        time_unit = time_filter[i:].strip().lower()
                        if time_value and time_unit in ["ms", "s", "sec", "m", "min"]:
                            valid_format = True
                        break
                
                if valid_format or time_filter.isdigit():
                    break
                else:
                    print_warning("无效的格式，请输入类似'>100ms'的格式。")
            except Exception as e:
                print_error(f"输入错误: {e}")
        
        filters["time"] = time_filter
        print_filter_summary("已选择时间筛选", time_filter)
        
        # 5. URL关键词筛选
        print_subheader("URL关键词筛选")
        url_filter = input(f"{Fore.YELLOW}请输入URL关键词 (直接回车选择'全部'): {Style.RESET_ALL}").strip()
        filters["url"] = url_filter if url_filter else "全部"
        print_filter_summary("已选择URL关键词筛选", filters['url'])
        
        # 显示筛选选项汇总
        print_subheader("筛选选项汇总")
        print_filter_summary("资源类型", resource_types_display)
        print_filter_summary("状态码", status_filter)
        print_filter_summary("大小", filters['size'])
        print_filter_summary("时间", filters['time'])
        print_filter_summary("URL关键词", filters['url'])
        
        # 执行筛选 - 移除确认步骤，直接执行
        print_processing("\n正在筛选...")
        filtered_entries = apply_filters(entries, filters, verbose)
        
        # 如果没有符合条件的条目
        if not filtered_entries:
            print_warning("没有符合筛选条件的条目。")
            return
        
        # 创建输出文件名
        if not output_file:
            output_file = generate_output_filename(input_file, filters)
        
        # 使用绝对路径
        if not os.path.isabs(output_file):
            output_file = os.path.abspath(output_file)
        
        # 写入输出文件
        try:
            write_filtered_har(har_data, filtered_entries, output_file)
            print_success(f"\n筛选完成! 共筛选出 {len(filtered_entries)} 个条目。")
            print_file_info(f"已保存到文件: {output_file}")
        except Exception as e:
            print_error(f"写入输出文件时出错: {str(e)}")
            
    except KeyboardInterrupt:
        logging.info("用户中断操作")
        print_warning("\n操作已取消。")
    except Exception as e:
        logging.error(f"执行过程中出错: {str(e)}", exc_info=True)
        print_error(f"错误: {str(e)}")
        if verbose:
            print("\n详细错误信息:")
            traceback.print_exc()
        else:
            print_info("如需查看详细错误信息，请使用 -v 或 --verbose 选项。")

# 获取可用的状态码筛选选项
def get_available_status_filters() -> List[str]:
    """
    获取可用的状态码筛选选项
    
    Returns:
        List: 可用的状态码筛选选项列表
    """
    return [
        "全部",
        "成功 (2xx)",
        "重定向 (3xx)",
        "客户端错误 (4xx)",
        "服务器错误 (5xx)",
        "错误 (4xx-5xx)"
    ]

# 将状态码筛选选项转换为内部代码
def translate_status_filter(status_filter: str) -> str:
    """
    将状态码筛选选项转换为内部代码
    
    Args:
        status_filter: 状态码筛选选项
        
    Returns:
        str: 对应的内部代码
    """
    translation = {
        "全部": "all",
        "成功 (2xx)": "成功",
        "重定向 (3xx)": "重定向",
        "客户端错误 (4xx)": "客户端错误",
        "服务器错误 (5xx)": "服务器错误",
        "错误 (4xx-5xx)": "错误"
    }
    return translation.get(status_filter, status_filter)

# 获取可用的资源类型列表
def get_available_resource_types() -> List[str]:
    """
    获取可用的资源类型筛选选项
    
    Returns:
        List: 可用的资源类型列表
    """
    return [
        "全部", 
        "Fetch/XHR", 
        "JS", 
        "CSS", 
        "Img", 
        "Media", 
        "Font", 
        "Doc", 
        "WS", 
        "Wasm",
        "Manifest",
        "其他"
    ]

# 将中文筛选选项转换为英文代码
def translate_resource_type(resource_type_zh: str) -> str:
    """
    将中文资源类型转换为英文代码
    
    Args:
        resource_type_zh: 中文资源类型
        
    Returns:
        str: 对应的英文代码
    """
    translation = {
        "全部": "all",
        "Fetch/XHR": "fetch/xhr",
        "JS": "js",
        "CSS": "css",
        "Img": "img",
        "Media": "media",
        "Font": "font",
        "Doc": "doc",
        "WS": "ws",
        "Wasm": "wasm",
        "Manifest": "manifest",
        "其他": "other"
    }
    return translation.get(resource_type_zh, resource_type_zh.lower())

# 执行筛选逻辑
def apply_filters(entries: List[Dict[str, Any]], filters: Dict[str, Any], verbose: bool = False) -> List[Dict[str, Any]]:
    """
    应用所有筛选条件到条目列表
    
    Args:
        entries: HAR条目列表
        filters: 筛选条件字典，包含各种筛选选项
        verbose: 是否输出详细信息
        
    Returns:
        List: 筛选后的HAR条目列表
    """
    filtered_entries = entries
    
    # 应用资源类型筛选
    if "resource_type" in filters:
        resource_types = filters["resource_type"]
        # 如果是单个资源类型且为"all"，则跳过筛选
        if isinstance(resource_types, str) and resource_types == "all":
            pass
        # 如果是列表且包含"all"，则跳过筛选
        elif isinstance(resource_types, list) and "all" in resource_types:
            pass
        # 否则进行筛选
        else:
            filtered_entries = filter_by_resource_type(filtered_entries, resource_types)
            if verbose:
                print_stats(f"资源类型筛选后剩余 {len(filtered_entries)} 个条目。")
    
    # 应用状态码筛选
    if "status" in filters and filters["status"] != "all":
        filtered_entries = filter_by_status(filtered_entries, filters["status"])
        if verbose:
            print_stats(f"状态码筛选后剩余 {len(filtered_entries)} 个条目。")
    
    # 应用大小筛选
    if "size" in filters and filters["size"] != "全部":
        filtered_entries = filter_by_size(filtered_entries, filters["size"])
        if verbose:
            print_stats(f"大小筛选后剩余 {len(filtered_entries)} 个条目。")
    
    # 应用时间筛选
    if "time" in filters and filters["time"] != "全部":
        filtered_entries = filter_by_time(filtered_entries, filters["time"])
        if verbose:
            print_stats(f"时间筛选后剩余 {len(filtered_entries)} 个条目。")
    
    # 应用URL关键词筛选
    if "url" in filters and filters["url"] != "全部":
        filtered_entries = filter_by_url(filtered_entries, filters["url"])
        if verbose:
            print_stats(f"URL关键词筛选后剩余 {len(filtered_entries)} 个条目。")
    
    return filtered_entries

# 生成输出文件名
def generate_output_filename(input_file: str, filters: Dict[str, Any]) -> str:
    """
    根据输入文件名和筛选条件生成输出文件名
    
    Args:
        input_file: 输入文件路径
        filters: 筛选条件字典
        
    Returns:
        str: 生成的输出文件名
    """
    # 获取原文件名（不含扩展名）
    base_name = os.path.basename(input_file)
    file_name_without_ext = os.path.splitext(base_name)[0]
    
    # 生成筛选类型字符串
    filter_type = ""
    
    # 处理资源类型
    if "resource_type" in filters:
        resource_types = filters["resource_type"]
        
        # 如果是单个资源类型
        if isinstance(resource_types, str):
            if resource_types != "all":
                # 替换文件名中的非法字符
                safe_resource_type = resource_types.replace("/", "_").replace("\\", "_").replace(":", "_")
                filter_type += f"_{safe_resource_type}"
        # 如果是多个资源类型
        elif isinstance(resource_types, list):
            if "all" not in resource_types:
                # 连接多个资源类型，并替换非法字符
                resource_types_str = "+".join(rt for rt in resource_types)
                safe_resource_types = resource_types_str.replace("/", "_").replace("\\", "_").replace(":", "_")
                filter_type += f"_{safe_resource_types}"
    
    if "status" in filters and filters["status"] != "all":
        # 替换文件名中的非法字符
        safe_status = filters["status"].replace("/", "_").replace("\\", "_").replace(":", "_")
        filter_type += f"_{safe_status}"
    
    if "size" in filters and filters["size"] != "全部":
        # 替换文件名中的非法字符
        safe_size = filters["size"].replace("/", "_").replace("\\", "_").replace(":", "_").replace(">", "gt").replace("<", "lt").replace("=", "eq")
        filter_type += f"_{safe_size}"
    
    if "time" in filters and filters["time"] != "全部":
        # 替换文件名中的非法字符
        safe_time = filters["time"].replace("/", "_").replace("\\", "_").replace(":", "_").replace(">", "gt").replace("<", "lt").replace("=", "eq")
        filter_type += f"_{safe_time}"
    
    if "url" in filters and filters["url"] != "全部":
        # 限制URL关键词长度并替换非法字符
        url_part = filters["url"][:10]
        safe_url = url_part.replace("/", "_").replace("\\", "_").replace(":", "_")
        filter_type += f"_url-{safe_url}"
    
    # 如果没有筛选条件，使用"all"
    if not filter_type:
        filter_type = "_all"
    
    # 生成时间戳
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建输出文件名
    return f"{file_name_without_ext}{filter_type}_{timestamp}.har"

# 将筛选后的条目写入新的HAR文件
def write_filtered_har(har_data: Dict[str, Any], filtered_entries: List[Dict[str, Any]], output_file: str) -> None:
    """
    将筛选后的条目写入新的HAR文件
    
    Args:
        har_data: 原始HAR数据
        filtered_entries: 筛选后的条目列表
        output_file: 输出文件路径
    """
    # 创建新的HAR对象，保留原始结构但替换entries
    filtered_har = har_data.copy()
    filtered_har["log"]["entries"] = filtered_entries
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(filtered_har, f, ensure_ascii=False, indent=2)

def main():
    """
    主函数，处理命令行参数并执行筛选流程
    """
    parser = argparse.ArgumentParser(description='筛选.har文件并输出到新文件')
    parser.add_argument('input_file', help='输入的.har文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径(可选，默认为自动生成)')
    parser.add_argument('-i', '--interactive', action='store_true', help='启用交互模式')
    parser.add_argument('-t', '--type', help='资源类型筛选，例如：Fetch/XHR, CSS, JS, Img等。多个类型使用逗号分隔，例如：Fetch/XHR,JS')
    parser.add_argument('-s', '--status', help='状态码筛选，例如：200, 404, 2xx, "成功"等')
    parser.add_argument('-z', '--size', help='大小筛选，例如：>100KB, <1MB等')
    parser.add_argument('--time', help='时间筛选，例如：>100ms, <1s等')
    parser.add_argument('-u', '--url', help='URL关键词筛选')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    # 设置日志级别
    setup_logging(args.verbose)
    
    if not os.path.exists(args.input_file):
        logging.error(f"找不到输入文件 '{args.input_file}'")
        print_error(f"找不到输入文件 '{args.input_file}'")
        sys.exit(1)
    
    try:
        # 确保输入文件使用绝对路径
        input_file = os.path.abspath(args.input_file)
        
        # 如果启用交互模式或未提供其他筛选参数，则进入交互模式
        if args.interactive or (not args.type and not args.status and not args.size 
                              and not args.time and not args.url):
            interactive_mode(input_file, args.output, args.verbose)
        else:
            # 读取输入文件
            if args.verbose:
                print_processing(f"正在读取文件: {input_file}...")
            
            har_data = read_har_file(input_file)
            entries = get_entries(har_data)
            
            if args.verbose:
                print_success(f"成功读取 {len(entries)} 个请求条目。")
            
            # 收集筛选选项
            filters = {}
            
            # 资源类型筛选
            if args.type:
                # 处理多个资源类型（逗号分隔）
                resource_types_input = args.type.split(',')
                resource_types = []
                
                for rt in resource_types_input:
                    resource_type_str = rt.strip()
                    resource_types.append(resource_type_str)
                
                # 如果只有一个资源类型，转换为单个字符串
                if len(resource_types) == 1:
                    filters["resource_type"] = translate_resource_type(resource_types[0])
                else:
                    # 多个资源类型，转换为编码列表
                    filters["resource_type"] = [translate_resource_type(rt) for rt in resource_types]
                
                if args.verbose:
                    resource_types_display = ", ".join(resource_types)
                    print_filter_summary("使用资源类型筛选", resource_types_display)
            
            # 状态码筛选
            if args.status:
                status_filter = args.status
                filters["status"] = translate_status_filter(status_filter)
                if args.verbose:
                    print_filter_summary("使用状态码筛选", status_filter)
            
            # 大小筛选
            if args.size:
                filters["size"] = args.size
                if args.verbose:
                    print_filter_summary("使用大小筛选", args.size)
            
            # 时间筛选
            if args.time:
                filters["time"] = args.time
                if args.verbose:
                    print_filter_summary("使用时间筛选", args.time)
            
            # URL关键词筛选
            if args.url:
                filters["url"] = args.url
                if args.verbose:
                    print_filter_summary("使用URL关键词筛选", args.url)
            
            # 执行筛选
            if args.verbose:
                print_processing("\n正在筛选...")
            
            filtered_entries = apply_filters(entries, filters, args.verbose)
            
            # 如果没有符合条件的条目
            if not filtered_entries:
                print_warning("没有符合筛选条件的条目。")
                return
            
            # 创建输出文件名
            output_file = args.output if args.output else generate_output_filename(input_file, filters)
            
            # 确保输出文件使用绝对路径
            if not os.path.isabs(output_file):
                output_file = os.path.abspath(output_file)
            
            # 写入输出文件
            write_filtered_har(har_data, filtered_entries, output_file)
            
            print_success(f"\n筛选完成! 共筛选出 {len(filtered_entries)} 个条目。")
            print_file_info(f"已保存到文件: {output_file}")
            
    except KeyboardInterrupt:
        logging.info("用户中断操作")
        print_warning("\n操作已取消。")
    except Exception as e:
        logging.error(f"执行过程中出错: {str(e)}", exc_info=True)
        print_error(f"错误: {str(e)}")
        if args.verbose:
            print("\n详细错误信息:")
            traceback.print_exc()
        else:
            print_info("如需查看详细错误信息，请使用 -v 或 --verbose 选项。")

if __name__ == "__main__":
    main() 