import requests
import json
import re
from urllib.parse import urlparse, parse_qs

# cookie固定的前缀
prefix = 'user_01000000000000000000000000%3A%3A'
# 填写账号token部分
cursor_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzEzQUYxNjlBWk1EUjFCSFdIUkpaTVhWIiwidGltZSI6IjE3NTM1MzIxOTgiLCJyYW5kb21uZXNzIjoiNWJkNzBhNjAtMTE3Mi00NmI3IiwiZXhwIjoxNzU4NzE2MTk4LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.K0lZkK3JLrtk7EWaGvBKX6apwF4ZHMQWSrT4T0Qkzz0'

# 拼接完整的token
full_token = prefix + cursor_token

class CursorSubscriptionManager:
    def __init__(self, token):
        self.cursor_token = token
        self.session = requests.Session()
        self.stripe_session_url = None
        self.live_session_id = None
        self.bps_session_id = None
        self.subscription_id = None
        self.csrf_token = None
        self.bearer_token = None
        
    def get_stripe_session_url(self):
        """获取Stripe会话URL"""
        cookies = {
            'NEXT_LOCALE': 'en',
            'WorkosCursorSessionToken': self.cursor_token
        }
        
        headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'if-none-match': '"zc6lt7pm693k"',
            'priority': 'u=1, i',
            'referer': 'https://cursor.com/dashboard',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        }
        
        try:
            response = self.session.get('https://cursor.com/api/stripeSession', 
                                      cookies=cookies, headers=headers)
            if response.status_code == 200:
                stripe_url = response.text.strip().replace('"', '')
                self.stripe_session_url = stripe_url
                # 从URL中提取live session_id
                url_parts = stripe_url.split('/')
                if 'session' in url_parts:
                    session_index = url_parts.index('session')
                    if session_index + 1 < len(url_parts):
                        self.live_session_id = url_parts[session_index + 1]
                print(f"✅ 获取Stripe会话成功!")
                print(f"🔗 Session URL: {stripe_url}")
                print(f"📝 Live Session ID: {self.live_session_id}")
                return True
            else:
                print(f"❌ 获取Stripe会话失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return False
    
    def get_session_info(self):
        """获取会话信息，包括Bearer token和真实的bps session ID"""
        if not self.live_session_id:
            print("❌ 没有live_session_id，请先获取Stripe会话")
            return False
            
        # 访问Stripe会话页面获取页面内容
        try:
            response = self.session.get(self.stripe_session_url)
            if response.status_code == 200:
                print("✅ 成功访问Stripe会话页面")
                html_content = response.text
                
                # 从cookies中提取CSRF token
                for cookie in self.session.cookies:
                    if 'csrf' in cookie.name.lower():
                        self.csrf_token = cookie.value
                        print(f"📝 找到CSRF token: {self.csrf_token[:50]}...")
                        break
                
                # 从页面HTML中提取session_api_key (Bearer token)
                bearer_patterns = [
                    r'"session_api_key":"(ek_live_[^"]+)"',
                    r'&quot;session_api_key&quot;:&quot;(ek_live_[^&]+)&quot;',
                    r'session_api_key["\']?\s*:\s*["\']?(ek_live_[^"\']+)["\']?'
                ]
                
                for i, pattern in enumerate(bearer_patterns, 1):
                    match = re.search(pattern, html_content)
                    if match:
                        self.bearer_token = match.group(1)
                        print(f"📝 找到Bearer token (模式{i}): {self.bearer_token[:50]}...")
                        break
                
                # 从页面HTML中提取portal_session_id (真实的bps_ session ID)
                bps_patterns = [
                    r'"portal_session_id":"(bps_[^"]+)"',
                    r'&quot;portal_session_id&quot;:&quot;(bps_[^&]+)&quot;',
                    r'portal_session["\']?\s*:\s*["\']?(bps_[^"\']+)["\']?',
                    r'"id":"(bps_[^"]+)".*"object":"billing_portal\.session"'
                ]
                
                for i, pattern in enumerate(bps_patterns, 1):
                    match = re.search(pattern, html_content, re.DOTALL)
                    if match:
                        self.bps_session_id = match.group(1)
                        print(f"📝 找到BPS Session ID (模式{i}): {self.bps_session_id}")
                        break
                
                if not self.bearer_token:
                    print("⚠️ 未找到Bearer token")
                    return False
                    
                if not self.bps_session_id:
                    print("⚠️ 未找到BPS Session ID，尝试通过API获取...")
                    return self.get_bps_session_from_api()
                
                return True
            else:
                print(f"❌ 访问Stripe会话页面失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取会话信息异常: {str(e)}")
            return False
    
    def get_bps_session_from_api(self):
        """通过API获取真实的bps session ID"""
        if not self.bearer_token:
            print("❌ 缺少Bearer token")
            return False
            
        # 尝试多个可能的API端点来获取session信息
        possible_urls = [
            f"https://billing.stripe.com/v1/billing_portal/sessions/{self.live_session_id}",
            "https://billing.stripe.com/v1/billing_portal/sessions"
        ]
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        for url in possible_urls:
            try:
                response = self.session.get(url, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    if 'id' in data and data['id'].startswith('bps_'):
                        self.bps_session_id = data['id']
                        print(f"📝 通过API获取到BPS Session ID: {self.bps_session_id}")
                        return True
                else:
                    print(f"⚠️ API请求失败 {url}: {response.status_code}")
            except Exception as e:
                print(f"⚠️ API请求异常 {url}: {str(e)}")
        
        return False
    
    def get_subscription_id(self):
        """获取订阅ID"""
        if not self.bps_session_id:
            print("❌ 没有BPS session_id")
            return False
        
        if not self.bearer_token:
            print("❌ 没有Bearer token，无法进行API调用")
            return False
            
        # 构建获取订阅列表的URL
        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions"
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'referer': self.stripe_session_url,
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest'
        }
        
        # 如果有CSRF token，也添加进去
        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token
        
        params = {
            'include_only[]': [
                'has_more',
                'data.id',
                'data.status',
                'data.is_cancelable'
            ]
        }
        
        try:
            response = self.session.get(url, headers=headers, params=params)
            if response.status_code == 200:
                data = response.json()
                print(f"📊 订阅列表响应: {json.dumps(data, indent=2)}")
                if 'data' in data and len(data['data']) > 0:
                    for subscription in data['data']:
                        if subscription.get('is_cancelable', False):
                            self.subscription_id = subscription['id']
                            print(f"✅ 找到可取消的订阅: {self.subscription_id}")
                            return True
                    print("❌ 没有找到可取消的订阅")
                    return False
                else:
                    print("❌ 没有找到订阅数据")
                    return False
            else:
                print(f"❌ 获取订阅列表失败: {response.status_code}")
                print(f"响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 获取订阅ID异常: {str(e)}")
            return False
    
    def cancel_subscription(self, refund=False):
        """取消订阅"""
        if not all([self.bps_session_id, self.subscription_id]):
            print("❌ 缺少必要的参数 (bps_session_id 或 subscription_id)")
            return False
        
        if not self.bearer_token:
            print("❌ 没有Bearer token，无法进行API调用")
            return False
        
        # 构建取消订阅的URL
        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions/{self.subscription_id}/cancel"
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'content-type': 'application/x-www-form-urlencoded',
            'origin': 'https://billing.stripe.com',
            'referer': f"https://billing.stripe.com/p/session/{self.live_session_id}/subscriptions/{self.subscription_id}/cancel",
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest'
        }
        
        # 如果有CSRF token，也添加进去
        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token
        
        params = {
            'include_only[]': 'id'
        }
        
        data = {
            'refund': 'true' if refund else 'false'
        }
        
        try:
            print(f"🚀 开始取消订阅...")
            print(f"📝 BPS Session ID: {self.bps_session_id}")
            print(f"📝 Subscription ID: {self.subscription_id}")
            print(f"📝 Refund: {refund}")
            
            response = self.session.post(url, headers=headers, params=params, data=data)
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📊 响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if 'id' in result:
                    print(f"✅ 订阅取消成功!")
                    print(f"🎯 已取消的订阅ID: {result['id']}")
                    return True
                else:
                    print("❌ 取消订阅响应格式异常")
                    return False
            else:
                print(f"❌ 取消订阅失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"错误详情: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"错误响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 取消订阅异常: {str(e)}")
            return False
    
    def get_subscription_status(self):
        """获取详细的订阅状态信息"""
        if not self.bps_session_id:
            print("❌ 没有BPS session_id")
            return False
        
        if not self.bearer_token:
            print("❌ 没有Bearer token，无法进行API调用")
            return False
            
        # 构建获取详细订阅信息的URL
        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions"
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'referer': self.stripe_session_url,
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest'
        }
        
        # 如果有CSRF token，也添加进去
        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token
        
        # 获取详细的订阅信息
        params = {
            'expand[]': [
                'data.items.price_details'
            ],
            'include_only[]': [
                'has_more',
                'data.id',
                'data.status',
                'data.created',
                'data.current_period_end',
                'data.trial_end',
                'data.cancel_at',
                'data.cancel_at_period_end',
                'data.is_cancelable',
                'data.items.id',
                'data.items.quantity',
                'data.items.price_details.unit_amount',
                'data.items.price_details.currency',
                'data.items.price_details.recurring.interval',
                'data.items.price_details.product.name'
            ]
        }
        
        try:
            response = self.session.get(url, headers=headers, params=params)
            if response.status_code == 200:
                data = response.json()
                return self.display_subscription_status(data)
            else:
                print(f"❌ 获取订阅状态失败: {response.status_code}")
                print(f"响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 获取订阅状态异常: {str(e)}")
            return False
    
    def display_subscription_status(self, data):
        """显示订阅状态信息"""
        import datetime
        
        print("\n" + "="*50)
        print("📊 CURSOR 订阅状态报告")
        print("="*50)
        
        if 'data' not in data or len(data['data']) == 0:
            print("❌ 没有找到任何订阅")
            print("🔍 订阅状态: 未订阅")
            return True
        
        subscription = data['data'][0]  # 获取第一个订阅
        
        # 订阅基本信息
        status = subscription.get('status', 'unknown')
        subscription_id = subscription.get('id', 'N/A')
        created = subscription.get('created')
        
        # 检查是否已设置取消
        cancel_at = subscription.get('cancel_at')
        cancel_at_period_end = subscription.get('cancel_at_period_end', False)
        is_scheduled_for_cancellation = cancel_at or cancel_at_period_end
        
        # 简单直接的取消状态显示
        if is_scheduled_for_cancellation:
            cancellation_status = "✅ 已取消"
        else:
            cancellation_status = "❌ 未取消"
        
        print(f"🎯 订阅续费状态: {cancellation_status}")
        print(f"🆔 订阅ID: {subscription_id}")
        
        if created:
            created_date = datetime.datetime.fromtimestamp(created).strftime('%Y-%m-%d %H:%M:%S')
            print(f"📅 创建时间: {created_date}")
        
        # 试用信息
        trial_end = subscription.get('trial_end')
        if trial_end:
            trial_end_date = datetime.datetime.fromtimestamp(trial_end).strftime('%Y-%m-%d %H:%M:%S')
            now = datetime.datetime.now()
            trial_end_dt = datetime.datetime.fromtimestamp(trial_end)
            
            if now < trial_end_dt:
                days_left = (trial_end_dt - now).days
                print(f"🚀 试用到期: {trial_end_date} (还剩 {days_left} 天)")
            else:
                print(f"⏰ 试用已结束: {trial_end_date}")
        
        # 计费周期信息
        current_period_end = subscription.get('current_period_end')
        if current_period_end:
            period_end_date = datetime.datetime.fromtimestamp(current_period_end).strftime('%Y-%m-%d %H:%M:%S')
            print(f"📆 当前周期结束: {period_end_date}")
        
        # 取消信息
        if cancel_at:
            cancel_date = datetime.datetime.fromtimestamp(cancel_at).strftime('%Y-%m-%d %H:%M:%S')
            print(f"⏹️ 将于以下时间停止服务: {cancel_date}")
        elif cancel_at_period_end:
            print(f"⏹️ 将在当前周期结束时停止服务")
        
        # 产品信息
        items = subscription.get('items', [])
        if items:
            print(f"\n💰 订阅产品:")
            for item in items:
                price_details = item.get('price_details', {})
                product = price_details.get('product', {})
                product_name = product.get('name', 'Unknown Product')
                unit_amount = price_details.get('unit_amount', 0)
                currency = price_details.get('currency', 'usd').upper()
                recurring = price_details.get('recurring', {})
                interval = recurring.get('interval', 'month')
                quantity = item.get('quantity', 1)
                
                price = unit_amount / 100  # 转换为实际价格
                print(f"  📦 {product_name}")
                print(f"  💵 价格: {price:.2f} {currency} / {interval}")
                print(f"  🔢 数量: {quantity}")
        
        # 可操作性
        print(f"\n🛠️ 可执行操作:")
        if is_scheduled_for_cancellation:
            print("  ✅ 订阅已取消，无需进一步操作")
        else:
            is_cancelable = subscription.get('is_cancelable', False)
            if is_cancelable:
                print("  ⚠️ 可以取消订阅")
            else:
                print("  ℹ️ 暂时无法取消")
        
        print("="*50)
        return True
    
    def view_subscription_status_flow(self):
        """查看订阅状态的完整流程"""
        print("🎯 开始查看订阅状态...")
        
        # 步骤1: 获取Stripe会话URL
        if not self.get_stripe_session_url():
            return False
        
        # 步骤2: 获取会话信息（Bearer token和BPS session ID）
        if not self.get_session_info():
            return False
        
        # 步骤3: 获取订阅状态
        return self.get_subscription_status()
    
    def cancel_subscription_flow(self, refund=False):
        """完整的取消订阅流程"""
        print("🎯 开始取消订阅流程...")
        
        # 步骤1: 获取Stripe会话URL
        if not self.get_stripe_session_url():
            return False
        
        # 步骤2: 获取会话信息（Bearer token和BPS session ID）
        if not self.get_session_info():
            return False
        
        # 步骤3: 获取订阅ID
        if not self.get_subscription_id():
            return False
        
        # 步骤4: 取消订阅
        return self.cancel_subscription(refund)

def main():
    print("🎯 CURSOR 订阅管理工具")
    print("=" * 40)
    print("1. 📊 查看订阅状态")
    print("2. ❌ 取消订阅")
    print("3. 🚪 退出")
    print("=" * 40)
    
    while True:
        try:
            choice = input("\n请选择操作 (1-3): ").strip()
            
            if choice == '1':
                # 查看订阅状态
                manager = CursorSubscriptionManager(full_token)
                success = manager.view_subscription_status_flow()
                if success:
                    print("\n✅ 订阅状态查看完成!")
                else:
                    print("\n❌ 订阅状态查看失败!")
                    
            elif choice == '2':
                # 取消订阅 - 直接执行，无需确认
                print("\n🚀 正在执行取消订阅...")
                
                manager = CursorSubscriptionManager(full_token)
                success = manager.cancel_subscription_flow(refund=False)
                
                if success:
                    print("\n🎉 订阅取消流程完成!")
                    print("💡 提示: 订阅已取消，但在当前周期结束前仍可正常使用")
                else:
                    print("\n💥 订阅取消流程失败!")
                    
            elif choice == '3':
                print("👋 感谢使用，再见!")
                break
                
            else:
                print("❌ 无效选择，请输入 1-3")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")

if __name__ == "__main__":
    main() 