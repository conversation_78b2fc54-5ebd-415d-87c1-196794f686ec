import requests
import json
import re
import datetime
from urllib.parse import urlparse, parse_qs

# cookie固定的前缀
prefix = 'user_01000000000000000000000000%3A%3A'
# 填写账号token部分
cursor_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzEyWE5DQ1lQSzMzWFY0R1hYQVQxVEFaIiwidGltZSI6IjE3NTM1MTk2ODYiLCJyYW5kb21uZXNzIjoiYTUzMGUyMTQtMmQ0ZC00NWVjIiwiZXhwIjoxNzU4NzAzNjg2LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.hVewU43fb6nz1wGSuYjSljAvzEOnSAqG-lr7VSilSiU'

# 拼接完整的token
full_token = prefix + cursor_token

class CursorSubscriptionChecker:
    def __init__(self, token):
        self.cursor_token = token
        self.session = requests.Session()
        self.stripe_session_url = None
        self.live_session_id = None
        self.bps_session_id = None
        self.csrf_token = None
        self.bearer_token = None
        
    def get_stripe_session_url(self):
        """获取Stripe会话URL"""
        cookies = {
            'NEXT_LOCALE': 'en',
            'WorkosCursorSessionToken': self.cursor_token
        }
        
        headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'if-none-match': '"zc6lt7pm693k"',
            'priority': 'u=1, i',
            'referer': 'https://cursor.com/dashboard',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        }
        
        try:
            response = self.session.get('https://cursor.com/api/stripeSession', 
                                      cookies=cookies, headers=headers)
            if response.status_code == 200:
                stripe_url = response.text.strip().replace('"', '')
                self.stripe_session_url = stripe_url
                # 从URL中提取live session_id
                url_parts = stripe_url.split('/')
                if 'session' in url_parts:
                    session_index = url_parts.index('session')
                    if session_index + 1 < len(url_parts):
                        self.live_session_id = url_parts[session_index + 1]
                return True
            else:
                print(f"❌ 获取Stripe会话失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return False
    
    def get_session_info(self):
        """获取会话信息，包括Bearer token和真实的bps session ID"""
        if not self.live_session_id:
            print("❌ 没有live_session_id，请先获取Stripe会话")
            return False
            
        # 访问Stripe会话页面获取页面内容
        try:
            response = self.session.get(self.stripe_session_url)
            if response.status_code == 200:
                html_content = response.text
                
                # 从cookies中提取CSRF token
                for cookie in self.session.cookies:
                    if 'csrf' in cookie.name.lower():
                        self.csrf_token = cookie.value
                        break
                
                # 从页面HTML中提取session_api_key (Bearer token)
                bearer_patterns = [
                    r'"session_api_key":"(ek_live_[^"]+)"',
                    r'&quot;session_api_key&quot;:&quot;(ek_live_[^&]+)&quot;',
                    r'session_api_key["\']?\s*:\s*["\']?(ek_live_[^"\']+)["\']?'
                ]
                
                for pattern in bearer_patterns:
                    match = re.search(pattern, html_content)
                    if match:
                        self.bearer_token = match.group(1)
                        break
                
                # 从页面HTML中提取portal_session_id (真实的bps_ session ID)
                bps_patterns = [
                    r'"portal_session_id":"(bps_[^"]+)"',
                    r'&quot;portal_session_id&quot;:&quot;(bps_[^&]+)&quot;',
                    r'portal_session["\']?\s*:\s*["\']?(bps_[^"\']+)["\']?',
                    r'"id":"(bps_[^"]+)".*"object":"billing_portal\.session"'
                ]
                
                for pattern in bps_patterns:
                    match = re.search(pattern, html_content, re.DOTALL)
                    if match:
                        self.bps_session_id = match.group(1)
                        break
                
                if not self.bearer_token:
                    print("⚠️ 未找到Bearer token")
                    return False
                    
                if not self.bps_session_id:
                    print("⚠️ 未找到BPS Session ID")
                    return False
                
                return True
            else:
                print(f"❌ 访问Stripe会话页面失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取会话信息异常: {str(e)}")
            return False
    
    def get_subscription_status(self):
        """获取详细的订阅状态信息"""
        if not self.bps_session_id or not self.bearer_token:
            print("❌ 缺少必要的认证信息")
            return False
            
        # 构建获取详细订阅信息的URL
        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions"
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'referer': self.stripe_session_url,
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest'
        }
        
        # 如果有CSRF token，也添加进去
        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token
        
        # 获取详细的订阅信息
        params = {
            'expand[]': [
                'data.items.price_details'
            ],
            'include_only[]': [
                'has_more',
                'data.id',
                'data.status',
                'data.created',
                'data.current_period_end',
                'data.trial_end',
                'data.cancel_at',
                'data.cancel_at_period_end',
                'data.is_cancelable',
                'data.items.id',
                'data.items.quantity',
                'data.items.price_details.unit_amount',
                'data.items.price_details.currency',
                'data.items.price_details.recurring.interval',
                'data.items.price_details.product.name'
            ]
        }
        
        try:
            response = self.session.get(url, headers=headers, params=params)
            if response.status_code == 200:
                data = response.json()
                return self.display_subscription_status(data)
            else:
                print(f"❌ 获取订阅状态失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取订阅状态异常: {str(e)}")
            return False
    
    def display_subscription_status(self, data):
        """显示订阅状态信息"""
        print("\n" + "="*60)
        print("📊 CURSOR 订阅状态报告")
        print("="*60)
        
        if 'data' not in data or len(data['data']) == 0:
            print("❌ 没有找到任何订阅")
            print("🔍 订阅状态: 未订阅")
            print("💡 建议: 您可以前往 https://cursor.com/dashboard 开始订阅")
            print("="*60)
            return True
        
        subscription = data['data'][0]  # 获取第一个订阅
        
        # 订阅基本信息
        status = subscription.get('status', 'unknown')
        subscription_id = subscription.get('id', 'N/A')
        created = subscription.get('created')
        
        # 检查是否已设置取消
        cancel_at = subscription.get('cancel_at')
        cancel_at_period_end = subscription.get('cancel_at_period_end', False)
        is_scheduled_for_cancellation = cancel_at or cancel_at_period_end
        
        # 简单直接的取消状态显示
        if is_scheduled_for_cancellation:
            cancellation_status = "✅ 已取消"
        else:
            cancellation_status = "❌ 未取消"
        
        print(f"🎯 取消状态: {cancellation_status}")
        print(f"🆔 订阅ID: {subscription_id}")
        
        if created:
            created_date = datetime.datetime.fromtimestamp(created).strftime('%Y-%m-%d %H:%M:%S')
            print(f"📅 创建时间: {created_date}")
        
        # 试用信息
        trial_end = subscription.get('trial_end')
        if trial_end:
            trial_end_date = datetime.datetime.fromtimestamp(trial_end).strftime('%Y-%m-%d %H:%M:%S')
            now = datetime.datetime.now()
            trial_end_dt = datetime.datetime.fromtimestamp(trial_end)
            
            if now < trial_end_dt:
                days_left = (trial_end_dt - now).days
                hours_left = (trial_end_dt - now).seconds // 3600
                print(f"🚀 试用到期: {trial_end_date}")
                print(f"⏰ 剩余时间: {days_left} 天 {hours_left} 小时")
            else:
                print(f"⏰ 试用已结束: {trial_end_date}")
        
        # 计费周期信息
        current_period_end = subscription.get('current_period_end')
        if current_period_end:
            period_end_date = datetime.datetime.fromtimestamp(current_period_end).strftime('%Y-%m-%d %H:%M:%S')
            print(f"📆 当前周期结束: {period_end_date}")
        
                 # 取消信息
         if cancel_at:
             cancel_date = datetime.datetime.fromtimestamp(cancel_at).strftime('%Y-%m-%d %H:%M:%S')
             print(f"⏹️ 将于以下时间停止服务: {cancel_date}")
         elif cancel_at_period_end:
             print(f"⏹️ 将在当前周期结束时停止服务")
        
        # 产品信息
        items = subscription.get('items', [])
        if items:
            print(f"\n💰 订阅产品:")
            for item in items:
                price_details = item.get('price_details', {})
                product = price_details.get('product', {})
                product_name = product.get('name', 'Unknown Product')
                unit_amount = price_details.get('unit_amount', 0)
                currency = price_details.get('currency', 'usd').upper()
                recurring = price_details.get('recurring', {})
                interval = recurring.get('interval', 'month')
                quantity = item.get('quantity', 1)
                
                price = unit_amount / 100  # 转换为实际价格
                print(f"  📦 产品: {product_name}")
                print(f"  💵 价格: {price:.2f} {currency} / {interval}")
                print(f"  🔢 数量: {quantity}")
        
        # 状态说明
        print(f"\n💡 状态说明:")
        if is_scheduled_for_cancellation:
            print("  ✅ 订阅已取消，但在当前周期结束前仍可正常使用")
        else:
            if status == 'trialing':
                print("  🚀 您正在试用期内，可以免费使用所有功能")
            elif status == 'active':
                print("  🔄 您的订阅正常，可以正常使用所有功能")
            else:
                print("  📋 请检查您的订阅状态")
        
        # 可执行操作
        print(f"\n🛠️ 可执行操作:")
        if is_scheduled_for_cancellation:
            print("  ✅ 订阅已取消，无需进一步操作")
        else:
            is_cancelable = subscription.get('is_cancelable', False)
            if is_cancelable and status in ['active', 'trialing']:
                print("  ⚠️ 可以取消订阅")
                print("  💡 如需取消，请运行: python cancel_subscription_final.py")
            else:
                print("  ℹ️ 暂时无法取消")
        
        print("="*60)
        return True
    
    def check_status(self):
        """完整的查看订阅状态流程"""
        print("🎯 正在获取 CURSOR 订阅状态...")
        
        # 步骤1: 获取Stripe会话URL
        if not self.get_stripe_session_url():
            return False
        
        # 步骤2: 获取会话信息（Bearer token和BPS session ID）
        if not self.get_session_info():
            return False
        
        # 步骤3: 获取订阅状态
        return self.get_subscription_status()

def main():
    checker = CursorSubscriptionChecker(full_token)
    success = checker.check_status()
    
    if not success:
        print("\n❌ 订阅状态查看失败!")
    
    print(f"\n📝 查看时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 