import requests
import json
import re
from urllib.parse import urlparse, parse_qs

# cookie固定的前缀
prefix = 'user_01000000000000000000000000%3A%3A'
# 填写账号token部分
cursor_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzEyWE5DQ1lQSzMzWFY0R1hYQVQxVEFaIiwidGltZSI6IjE3NTM1MTk2ODYiLCJyYW5kb21uZXNzIjoiYTUzMGUyMTQtMmQ0ZC00NWVjIiwiZXhwIjoxNzU4NzAzNjg2LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.hVewU43fb6nz1wGSuYjSljAvzEOnSAqG-lr7VSilSiU'

# 拼接完整的token
full_token = prefix + cursor_token

class CursorSubscriptionManager:
    def __init__(self, token):
        self.cursor_token = token
        self.session = requests.Session()
        self.stripe_session_url = None
        self.live_session_id = None
        self.bps_session_id = None
        self.subscription_id = None
        self.csrf_token = None
        self.bearer_token = None
        
    def get_stripe_session_url(self):
        """获取Stripe会话URL"""
        cookies = {
            'NEXT_LOCALE': 'en',
            'WorkosCursorSessionToken': self.cursor_token
        }
        
        headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'if-none-match': '"zc6lt7pm693k"',
            'priority': 'u=1, i',
            'referer': 'https://cursor.com/dashboard',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        }
        
        try:
            response = self.session.get('https://cursor.com/api/stripeSession', 
                                      cookies=cookies, headers=headers)
            if response.status_code == 200:
                stripe_url = response.text.strip().replace('"', '')
                self.stripe_session_url = stripe_url
                # 从URL中提取live session_id
                url_parts = stripe_url.split('/')
                if 'session' in url_parts:
                    session_index = url_parts.index('session')
                    if session_index + 1 < len(url_parts):
                        self.live_session_id = url_parts[session_index + 1]
                print(f"✅ 获取Stripe会话成功!")
                print(f"🔗 Session URL: {stripe_url}")
                print(f"📝 Live Session ID: {self.live_session_id}")
                return True
            else:
                print(f"❌ 获取Stripe会话失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return False
    
    def get_session_info(self):
        """获取会话信息，包括Bearer token和真实的bps session ID"""
        if not self.live_session_id:
            print("❌ 没有live_session_id，请先获取Stripe会话")
            return False
            
        # 访问Stripe会话页面获取页面内容
        try:
            response = self.session.get(self.stripe_session_url)
            if response.status_code == 200:
                print("✅ 成功访问Stripe会话页面")
                html_content = response.text
                
                # 从cookies中提取CSRF token
                for cookie in self.session.cookies:
                    if 'csrf' in cookie.name.lower():
                        self.csrf_token = cookie.value
                        print(f"📝 找到CSRF token: {self.csrf_token[:50]}...")
                        break
                
                # 从页面HTML中提取session_api_key (Bearer token)
                bearer_patterns = [
                    r'"session_api_key":"(ek_live_[^"]+)"',
                    r'&quot;session_api_key&quot;:&quot;(ek_live_[^&]+)&quot;',
                    r'session_api_key["\']?\s*:\s*["\']?(ek_live_[^"\']+)["\']?'
                ]
                
                for i, pattern in enumerate(bearer_patterns, 1):
                    match = re.search(pattern, html_content)
                    if match:
                        self.bearer_token = match.group(1)
                        print(f"📝 找到Bearer token (模式{i}): {self.bearer_token[:50]}...")
                        break
                
                # 从页面HTML中提取portal_session_id (真实的bps_ session ID)
                bps_patterns = [
                    r'"portal_session_id":"(bps_[^"]+)"',
                    r'&quot;portal_session_id&quot;:&quot;(bps_[^&]+)&quot;',
                    r'portal_session["\']?\s*:\s*["\']?(bps_[^"\']+)["\']?',
                    r'"id":"(bps_[^"]+)".*"object":"billing_portal\.session"'
                ]
                
                for i, pattern in enumerate(bps_patterns, 1):
                    match = re.search(pattern, html_content, re.DOTALL)
                    if match:
                        self.bps_session_id = match.group(1)
                        print(f"📝 找到BPS Session ID (模式{i}): {self.bps_session_id}")
                        break
                
                if not self.bearer_token:
                    print("⚠️ 未找到Bearer token")
                    return False
                    
                if not self.bps_session_id:
                    print("⚠️ 未找到BPS Session ID，尝试通过API获取...")
                    return self.get_bps_session_from_api()
                
                return True
            else:
                print(f"❌ 访问Stripe会话页面失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取会话信息异常: {str(e)}")
            return False
    
    def get_bps_session_from_api(self):
        """通过API获取真实的bps session ID"""
        if not self.bearer_token:
            print("❌ 缺少Bearer token")
            return False
            
        # 尝试多个可能的API端点来获取session信息
        possible_urls = [
            f"https://billing.stripe.com/v1/billing_portal/sessions/{self.live_session_id}",
            "https://billing.stripe.com/v1/billing_portal/sessions"
        ]
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        for url in possible_urls:
            try:
                response = self.session.get(url, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    if 'id' in data and data['id'].startswith('bps_'):
                        self.bps_session_id = data['id']
                        print(f"📝 通过API获取到BPS Session ID: {self.bps_session_id}")
                        return True
                else:
                    print(f"⚠️ API请求失败 {url}: {response.status_code}")
            except Exception as e:
                print(f"⚠️ API请求异常 {url}: {str(e)}")
        
        return False
    
    def get_subscription_id(self):
        """获取订阅ID"""
        if not self.bps_session_id:
            print("❌ 没有BPS session_id")
            return False
        
        if not self.bearer_token:
            print("❌ 没有Bearer token，无法进行API调用")
            return False
            
        # 构建获取订阅列表的URL
        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions"
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'referer': self.stripe_session_url,
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest'
        }
        
        # 如果有CSRF token，也添加进去
        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token
        
        params = {
            'include_only[]': [
                'has_more',
                'data.id',
                'data.status',
                'data.is_cancelable'
            ]
        }
        
        try:
            response = self.session.get(url, headers=headers, params=params)
            if response.status_code == 200:
                data = response.json()
                print(f"📊 订阅列表响应: {json.dumps(data, indent=2)}")
                if 'data' in data and len(data['data']) > 0:
                    for subscription in data['data']:
                        if subscription.get('is_cancelable', False):
                            self.subscription_id = subscription['id']
                            print(f"✅ 找到可取消的订阅: {self.subscription_id}")
                            return True
                    print("❌ 没有找到可取消的订阅")
                    return False
                else:
                    print("❌ 没有找到订阅数据")
                    return False
            else:
                print(f"❌ 获取订阅列表失败: {response.status_code}")
                print(f"响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 获取订阅ID异常: {str(e)}")
            return False
    
    def cancel_subscription(self, refund=False):
        """取消订阅"""
        if not all([self.bps_session_id, self.subscription_id]):
            print("❌ 缺少必要的参数 (bps_session_id 或 subscription_id)")
            return False
        
        if not self.bearer_token:
            print("❌ 没有Bearer token，无法进行API调用")
            return False
        
        # 构建取消订阅的URL
        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions/{self.subscription_id}/cancel"
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'content-type': 'application/x-www-form-urlencoded',
            'origin': 'https://billing.stripe.com',
            'referer': f"https://billing.stripe.com/p/session/{self.live_session_id}/subscriptions/{self.subscription_id}/cancel",
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-requested-with': 'XMLHttpRequest'
        }
        
        # 如果有CSRF token，也添加进去
        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token
        
        params = {
            'include_only[]': 'id'
        }
        
        data = {
            'refund': 'true' if refund else 'false'
        }
        
        try:
            print(f"🚀 开始取消订阅...")
            print(f"📝 BPS Session ID: {self.bps_session_id}")
            print(f"📝 Subscription ID: {self.subscription_id}")
            print(f"📝 Refund: {refund}")
            
            response = self.session.post(url, headers=headers, params=params, data=data)
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📊 响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if 'id' in result:
                    print(f"✅ 订阅取消成功!")
                    print(f"🎯 已取消的订阅ID: {result['id']}")
                    return True
                else:
                    print("❌ 取消订阅响应格式异常")
                    return False
            else:
                print(f"❌ 取消订阅失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"错误详情: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"错误响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 取消订阅异常: {str(e)}")
            return False
    
    def cancel_subscription_flow(self, refund=False):
        """完整的取消订阅流程"""
        print("🎯 开始取消订阅流程...")
        
        # 步骤1: 获取Stripe会话URL
        if not self.get_stripe_session_url():
            return False
        
        # 步骤2: 获取会话信息（Bearer token和BPS session ID）
        if not self.get_session_info():
            return False
        
        # 步骤3: 获取订阅ID
        if not self.get_subscription_id():
            return False
        
        # 步骤4: 取消订阅
        return self.cancel_subscription(refund)

def main():
    # 初始化管理器
    manager = CursorSubscriptionManager(full_token)
    
    # 执行取消订阅流程
    success = manager.cancel_subscription_flow(refund=False)
    
    if success:
        print("\n🎉 订阅取消流程完成!")
    else:
        print("\n💥 订阅取消流程失败!")

if __name__ == "__main__":
    main() 